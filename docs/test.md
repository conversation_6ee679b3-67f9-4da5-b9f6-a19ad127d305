# 测试脚本

## 渠道商品映射关系

```bash
curl -i -X POST -H "Content-Type: application/json" 'http://127.0.0.1:8550/proxy/online.waiter-query.service.mailsaas/waiter-query/api/query/632c1fb83450113db4643a9a/kefu-ai-agents' -d{}

curl -i -X POST -H "Content-Type: application/json" 'http://127.0.0.1:8550/proxy/test4dp.yanxuan-ianus.service.mailsaas/waiter-query/api/query/63200bed6659de37c5a0b3b7/kefu-ai-agents' -d {}
```

## 问答消息意图总结

```bash
curl -i -X POST "http://localhost:8080/qa/message/intent" -H "Content-Type: application/json" -d '{
  "session_id": "session_123",
  "session_context": [
    {
      "content_type": 1,
      "source": 1,
      "content": "我想退货"
    }
  ],
  "current_message": {
    "content_type": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```

```bash
curl -i -X POST "http://localhost:8080/qa/message/intent" -H "Content-Type: application/json" -d '{
  "session_id": "session_123",
  "session_context": [
    {
      "content_type": 1,
      "source": 1,
      "content": "我想退货"
    },
    {
      "content_type": 4,
      "source": 1,
      "content": "{\"itemList\":[{\"skuId\":\"100007926902\"}]}"
    }
  ],
  "current_message": {
    "content_type": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```
