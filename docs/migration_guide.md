# 从 logging 到 Loguru 迁移指南

本指南帮助开发者将现有的 Python `logging` 代码迁移到 `loguru`。

## 迁移策略

项目提供了两种迁移策略：

1. **渐进式迁移**：保持现有代码不变，新代码使用 loguru
2. **完全迁移**：将所有代码迁移到 loguru

## 兼容性层

项目提供了兼容性层，确保现有代码无需修改即可工作：

### 原有代码（无需修改）

```python
from kefu_ai_agents.utils.logger import get_logger, setup_logging

# 初始化日志系统
setup_logging()

# 获取日志记录器
logger = get_logger(__name__)

# 使用方式完全相同
logger.info("这是信息")
logger.error("错误: %s", error_message)
logger.exception("发生异常")
```

### 新代码（推荐使用）

```python
from kefu_ai_agents.utils.logger import get_native_logger

# 获取原生 loguru 日志记录器
logger = get_native_logger()

# 使用 loguru 的特性
logger.info("用户 {user} 执行了 {action}", user="张三", action="登录")
logger.success("操作成功！")
```

## 功能对比

### 基本日志记录

| logging | loguru |
|---------|--------|
| `logger.debug(msg)` | `logger.debug(msg)` |
| `logger.info(msg)` | `logger.info(msg)` |
| `logger.warning(msg)` | `logger.warning(msg)` |
| `logger.error(msg)` | `logger.error(msg)` |
| `logger.critical(msg)` | `logger.critical(msg)` |
| 无 | `logger.success(msg)` ✨ |

### 格式化

| logging | loguru |
|---------|--------|
| `logger.info("用户 %s 登录", user)` | `logger.info("用户 {user} 登录", user=user)` |
| `logger.info(f"用户 {user} 登录")` | `logger.info("用户 {user} 登录", user=user)` |

### 异常记录

| logging | loguru |
|---------|--------|
| `logger.exception("错误")` | `logger.exception("错误")` |
| 需要手动格式化堆栈 | 自动美化堆栈信息 ✨ |

### 结构化日志

| logging | loguru |
|---------|--------|
| `logger.info("msg", extra={"key": "value"})` | `logger.info("msg", extra={"key": "value"})` |
| 需要自定义 Formatter | 内置支持 ✨ |

## 迁移步骤

### 步骤 1：更新导入

```python
# 旧的导入
from kefu_ai_agents.utils.logger import get_logger

# 新的导入（推荐）
from kefu_ai_agents.utils.logger import get_native_logger
```

### 步骤 2：更新日志记录器获取

```python
# 旧的方式
logger = get_logger(__name__)

# 新的方式
logger = get_native_logger(__name__)
```

### 步骤 3：更新日志调用

```python
# 旧的格式化方式
logger.info("用户 %s 执行了 %s", user, action)

# 新的格式化方式
logger.info("用户 {user} 执行了 {action}", user=user, action=action)
```

### 步骤 4：利用新特性

```python
# 使用 success 级别
logger.success("操作成功完成！")

# 使用上下文绑定
request_logger = logger.bind(request_id=request_id)
request_logger.info("处理请求")

# 使用结构化数据
logger.info("用户操作", extra={"user_id": 123, "action": "login"})
```

## 性能优化

### 使用装饰器记录执行时间

```python
from kefu_ai_agents.utils.logger import log_execution_time

@log_execution_time("用户登录")
def login_user(username, password):
    # 函数逻辑
    pass
```

### 使用便捷函数

```python
from kefu_ai_agents.utils.logger import log_info, log_error

# 简化的日志调用
log_info("这是信息")
log_error("这是错误")
```

## 配置迁移

### 环境变量对比

| 功能 | logging 配置 | loguru 配置 |
|------|-------------|-------------|
| 日志级别 | `LOG_LEVEL=INFO` | `LOG_LEVEL=INFO` |
| 日志文件 | `LOG_FILE=app.log` | `LOG_FILE=app.log` |
| 控制台输出 | `LOG_TO_CONSOLE=True` | `LOG_TO_CONSOLE=True` |
| 文件大小 | `LOG_MAX_SIZE=10485760` | `LOG_MAX_SIZE=10 MB` ✨ |
| 轮转方式 | `LOG_ROTATE_WHEN=midnight` | `LOG_ROTATE_WHEN=midnight` |
| 压缩 | 不支持 | 自动压缩 ✨ |
| 异步写入 | 不支持 | 自动启用 ✨ |

## 最佳实践

### 1. 渐进式迁移

```python
# 在新功能中使用 loguru
def new_feature():
    logger = get_native_logger()
    logger.info("新功能使用 loguru")

# 保持现有功能不变
def existing_feature():
    logger = get_logger(__name__)  # 兼容模式
    logger.info("现有功能保持不变")
```

### 2. 统一错误处理

```python
from kefu_ai_agents.utils.loguru_config import log_error_with_context

try:
    # 业务逻辑
    pass
except Exception as e:
    # 记录带上下文的错误
    context = {"user_id": user_id, "operation": "login"}
    log_error_with_context(e, context)
```

### 3. API 日志标准化

```python
from kefu_ai_agents.utils.loguru_config import log_api_request, log_api_response

# 在 API 处理中
log_api_request("POST", "/api/users", request_data)
# ... 处理逻辑 ...
log_api_response("POST", "/api/users", status_code, duration)
```

## 注意事项

1. **兼容性**：现有代码无需修改，可以继续使用
2. **性能**：loguru 提供更好的性能，特别是异步写入
3. **功能**：loguru 提供更多功能，如彩色输出、自动压缩等
4. **配置**：大部分配置保持兼容，新增了一些便利功能

## 故障排除

### 问题：日志没有输出

**解决方案**：检查 `LOG_LEVEL` 环境变量设置

```bash
export LOG_LEVEL=DEBUG
```

### 问题：文件日志不工作

**解决方案**：检查 `LOG_FILE` 环境变量和目录权限

```bash
export LOG_FILE=logs/app.log
mkdir -p logs
```

### 问题：颜色输出不正常

**解决方案**：确保终端支持颜色，或设置 `LOG_TO_CONSOLE=False`

## 更多资源

- [Loguru 官方文档](https://loguru.readthedocs.io/)
- [项目 Loguru 使用指南](loguru_guide.md)
- [示例代码](../examples/loguru_usage_example.py)
