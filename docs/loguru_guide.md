# Loguru 日志框架使用指南

本项目使用 [Lo<PERSON>ru](https://github.com/Delgan/loguru) 作为日志框架，它是一个简单而强大的 Python 日志库，提供了丰富的功能和优雅的 API。

## 目录

- [安装](#安装)
- [基本用法](#基本用法)
- [兼容性](#兼容性)
- [高级功能](#高级功能)
- [配置说明](#配置说明)
- [最佳实践](#最佳实践)

## 安装

Loguru 已经添加到项目依赖中，可以通过以下命令安装：

```bash
pip install -r requirements.txt
```

如果只想安装 Loguru：

```bash
pip install loguru
```

## 基本用法

### 1. 获取日志记录器

项目提供了两种方式获取日志记录器：

#### 兼容模式（推荐用于现有代码）

```python
from kefu_ai_agents.utils.logger import get_logger

# 获取与原有 logging 接口兼容的日志记录器
logger = get_logger(__name__)

# 使用方式与 logging 相同
logger.info("这是一条信息")
logger.error("发生错误: %s", error_message)
```

#### 原生模式（推荐用于新代码）

```python
from kefu_ai_agents.utils.logger import get_native_logger

# 获取原生 loguru 日志记录器
logger = get_native_logger(__name__)

# 使用 loguru 的特性
logger.info("用户 {user} 执行了 {action}", user="张三", action="登录")
logger.success("操作成功完成！")  # loguru 特有的 success 级别
```

### 2. 便捷函数

项目提供了一些便捷的日志函数：

```python
from kefu_ai_agents.utils.logger import log_info, log_error, log_debug, log_warning

# 直接使用便捷函数
log_info("这是一条信息")
log_error("发生错误")
```

### 3. 性能监控装饰器

使用装饰器记录函数执行时间：

```python
from kefu_ai_agents.utils.logger import log_execution_time

@log_execution_time("用户登录")
def login_user(username, password):
    # 函数执行完毕后会自动记录执行时间
    ...
```

## 兼容性

项目提供了与原有 `logging` 模块的兼容层，确保现有代码无需修改即可使用 loguru：

- `get_logger(name)` 返回与 `logging.Logger` 接口兼容的对象
- `setup_logging()` 保持与原有初始化函数相同的接口

## 高级功能

### 1. 结构化日志

```python
# 添加结构化数据
logger = get_native_logger()
logger.info("用户操作", extra={"user_id": 12345, "action": "购买", "amount": 99.99})

# 使用上下文绑定
request_logger = logger.bind(request_id="req_123", user_id=12345)
request_logger.info("处理请求")
```

### 2. 异常记录

```python
from kefu_ai_agents.utils.loguru_config import log_error_with_context

try:
    # 可能出错的代码
    result = 1 / 0
except Exception as e:
    # 记录带上下文的错误
    context = {
        "operation": "division",
        "operands": [1, 0],
        "user_id": 12345
    }
    log_error_with_context(e, context)
```

### 3. API 和数据库日志

```python
from kefu_ai_agents.utils.loguru_config import log_api_request, log_api_response, log_database_query

# 记录 API 请求和响应
log_api_request("POST", "/api/users", {"name": "张三"})
log_api_response("POST", "/api/users", 201, 0.05)  # 状态码和耗时

# 记录数据库查询
query = "SELECT * FROM users WHERE status = %s"
params = ("active",)
log_database_query(query, params, 0.02)  # 查询和耗时
```

## 配置说明

日志配置通过环境变量控制，可以在 `envs/loguru.env` 中查看完整配置示例：

```
# 基础配置
LOG_LEVEL=INFO                  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE=logs/app.log           # 日志文件路径
LOG_TO_CONSOLE=True             # 是否输出到控制台

# 文件轮转配置
LOG_MAX_SIZE=10 MB              # 单个日志文件最大大小
LOG_BACKUP_COUNT=5              # 保留的备份文件数量
LOG_ROTATE_WHEN=midnight        # 轮转时间: midnight, hourly, daily, weekly
```

## 最佳实践

1. **使用结构化日志**：尽量使用结构化数据记录日志，便于后期分析

   ```python
   # 好的做法
   logger.info("用户登录", extra={"user_id": 123, "ip": "***********"})
   
   # 避免这样做
   logger.info(f"用户 {user_id} 从 {ip} 登录")
   ```

2. **合理使用日志级别**：
   - `DEBUG`：详细的调试信息
   - `INFO`：常规信息
   - `WARNING`：警告但不影响运行
   - `ERROR`：错误但程序可以继续
   - `CRITICAL`：严重错误导致程序无法继续

3. **使用上下文绑定**：对于一个请求或操作，使用 `bind()` 绑定上下文

   ```python
   # 处理一个请求时
   request_logger = logger.bind(request_id=request_id, user_id=user_id)
   request_logger.info("开始处理请求")
   # ... 处理逻辑 ...
   request_logger.info("请求处理完成")
   ```

4. **记录异常时包含上下文**：使用 `log_error_with_context` 或 `logger.exception`

5. **使用性能监控装饰器**：对关键函数使用 `@log_execution_time` 装饰器

## 示例代码

完整的使用示例可以在 `examples/loguru_usage_example.py` 中查看。

## 更多资源

- [Loguru 官方文档](https://loguru.readthedocs.io/en/stable/index.html)
- [Loguru GitHub](https://github.com/Delgan/loguru)
