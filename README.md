# 客服 AI Agents 服务

一个基于 FastAPI 的智能客服意图识别和问答处理系统，集成了多种 AI 模型和外部服务，提供客户服务场景下的消息意图分析功能。

## 🚀 项目概述

本项目是一个面向客服场景的 AI 智能体服务，主要功能包括：

- **消息意图识别**：基于大语言模型分析客户消息的真实意图
- **多模态支持**：支持文本、图片、商品卡片、订单卡片等多种消息类型
- **会话上下文理解**：结合历史对话上下文提升意图识别准确性
- **商品信息关联**：自动解析商品卡片并关联商品信息
- **外部系统集成**：支持配置中心、数据库、缓存等多种外部服务

## 🏗️ 项目架构

```mermaid
graph TB
    A[客户端请求] --> B[FastAPI API 层]
    B --> C[Service 业务逻辑层]
    C --> D[Core 核心处理层]
    
    D --> E[消息意图识别 Agent]
    E --> F[外部 AI 模型服务]
    
    C --> G[Integration 集成层]
    G --> H[Apollo 配置中心]
    G --> I[Waiter 数据服务]
    G --> J[有灵 AI 服务]
    
    C --> K[Models 数据模型]
    C --> L[DB 数据访问层]
    L --> M[Redis 缓存]
    L --> N[MySQL 数据库]
    
    B --> O[Utils 工具层]
    O --> P[日志服务]
    O --> Q[图片处理]
```

## 📁 目录结构

```
kefu_ai_agents/
├── api/                      # API 层
│   ├── main.py              # FastAPI 应用主入口
│   ├── routers/             # 路由定义
│   │   ├── qa_api.py        # 问答 API
│   │   └── test_api.py      # 测试 API
│   └── schemas/             # 数据模型定义
│       ├── common_schemas.py # 通用响应模型
│       └── session_schemas.py # 会话相关模型
├── core/                    # 核心业务逻辑
│   └── message_intent_agent.py # 消息意图识别核心
├── service/                 # 业务服务层
│   ├── qa_service.py        # 问答服务
│   └── channel_item_service.py # 渠道商品服务
├── models/                  # 数据模型
│   └── qa_session_models.py # 问答会话模型
├── db/                      # 数据访问层
│   ├── redis_client.py      # Redis 客户端
│   └── data_warehouse.py    # 数据仓库
├── integration/             # 外部系统集成
│   ├── apolloy.py          # Apollo 配置中心
│   ├── waiter.py           # Waiter 数据服务
│   └── youling.py          # 有灵 AI 服务
├── utils/                   # 工具类
│   ├── logger.py           # 日志工具
│   └── image_utils.py      # 图片处理工具
└── web/                     # Web 相关（如有）
```

## ⚙️ 主要功能模块

### 1. 消息意图识别

核心功能模块，基于大语言模型分析客户消息的真实意图：

- 支持多种消息类型（文本、图片、卡片等）
- 结合会话上下文进行意图分析
- 自动识别商品相关信息
- 返回结构化的意图识别结果

### 2. 多模态消息处理

支持处理多种类型的消息内容：

- **文本消息**：直接文本内容
- **图片消息**：支持图片压缩和 Base64 编码
- **商品卡片**：解析商品 ID 和 SKU 信息
- **订单卡片**：处理订单相关信息
- **其他类型**：音频、视频、文件、链接等

### 3. 外部服务集成

- **Apollo 配置中心**：动态配置管理
- **有灵 AI 服务**：大语言模型 API 调用
- **Waiter 数据服务**：业务数据查询
- **Redis 集群**：缓存和会话存储

## 🛠️ 技术栈

- **Web 框架**：FastAPI
- **异步支持**：Uvicorn
- **AI 模型**：OpenAI API、Claude、GPT-4 等
- **数据库**：MySQL、Redis 集群
- **配置管理**：Apollo
- **图片处理**：PIL、loadimg
- **日志系统**：Loguru（现代化日志框架）
- **数据验证**：Pydantic

## 🚦 快速开始

### 1. 环境要求

- Python 3.8+
- Redis 集群
- MySQL 数据库
- Apollo 配置中心

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 环境配置

根据环境创建对应的环境变量文件：

**开发环境** (`envs/test.env`)：
```bash
# AI 服务配置
APP_ID=your_app_id
APP_KEY=your_app_key

# Redis 集群配置
REDIS_CLUSTER_NODES=localhost:6379
REDIS_PASSWORD=your_redis_password
REDIS_PREFIX=kefu_ai:

# Apollo 配置
APOLLO_APP_ID=your_apollo_app_id
APOLLO_CLUSTER=default
APOLLO_META_SERVER_URL=http://your-apollo-server

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_TO_CONSOLE=True
```

**生产环境** (`envs/online.env`)：
```bash
# 生产环境配置
LOG_LEVEL=ERROR
LOG_TO_CONSOLE=False
# ... 其他配置
```

### 4. 设置环境变量

```bash
# 测试环境
source setenv/setenv-test.sh

# 生产环境
source setenv/setenv-online.sh
```

### 5. 启动服务

```bash
# 开发模式
python kefu_ai_agents/api/main.py

# 或使用 uvicorn
uvicorn kefu_ai_agents.api.main:app --host 0.0.0.0 --port 8080 --reload

# 生产模式
gunicorn kefu_ai_agents.api.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### 6. 访问服务

- **API 文档**：http://localhost:8080/docs
- **健康检查**：http://localhost:8080/i/health
- **根路径**：http://localhost:8080/

## 📖 API 使用示例

### 消息意图识别

```bash
curl -X POST "http://localhost:8080/qa/message/intent" \
-H "Content-Type: application/json" \
-d '{
  "session_id": "session_123",
  "session_context": [
    {
      "content_type": 1,
      "source": 1,
      "content": "我想退货"
    }
  ],
  "current_message": {
    "content_type": 1,
    "source": 1,
    "content": "退货流程是什么？"
  }
}'
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "intent": "用户询问退货流程",
    "valid_message": true
  }
}
```

## 🔧 配置说明

### Apollo 配置中心

系统支持通过 Apollo 配置中心进行动态配置管理：

```json
{
  "qa_message_intent_agent": {
    "system_prompt": "你是一个客服意图识别助手...",
    "user_prompt_template": "用户咨询客服会话上文：{session_content}\n咨询商品：{session_item_name}\n用户当前提问：{current_question}\n用户的真实意图是（直接输出意图，不要回复其他与意图不相关内容）",
    "model_param": {
      "model": "gpt-4.1",
      "temperature": 0.1,
      "max_tokens": 200
    }
  }
}
```

### Redis 配置

支持 Redis 集群模式，配置示例：

```bash
REDIS_CLUSTER_NODES=node1:6379,node2:6379,node3:6379
REDIS_PASSWORD=your_password
REDIS_PREFIX=kefu_ai:
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试文件
python -m pytest tests/test_qa_service.py

# 查看覆盖率
python -m pytest --cov=kefu_ai_agents tests/
```

### 手动测试

```bash
# 测试消息意图识别
python kefu_ai_agents/core/message_intent_agent.py

# 测试 Redis 连接
python kefu_ai_agents/db/redis_client.py

# 测试 AI 服务
python kefu_ai_agents/integration/youling.py
```

## 🚀 部署

### Docker 部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8080
CMD ["gunicorn", "kefu_ai_agents.api.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8080"]
```

### CI/CD

项目使用 GitLab CI/CD 进行自动化部署，配置文件为 `.gitlab-ci.yml`。

## 📝 开发指南

### 添加新的意图识别逻辑

1. 在 `kefu_ai_agents/core/` 目录下创建新的 agent 文件
2. 在 `kefu_ai_agents/service/` 中添加对应的服务层逻辑
3. 在 `kefu_ai_agents/api/routers/` 中添加 API 路由
4. 更新相关的 schema 定义

### 添加新的外部服务集成

1. 在 `kefu_ai_agents/integration/` 目录下创建新的集成文件
2. 实现相应的客户端类和方法
3. 在服务层中调用集成的功能
4. 添加相应的配置项

## 🐛 故障排除

### 常见问题

1. **Redis 连接失败**
   - 检查 Redis 集群节点配置
   - 确认网络连通性和密码

2. **AI 服务调用超时**
   - 检查网络连接
   - 调整超时参数
   - 确认 API 密钥配置

3. **Apollo 配置获取失败**
   - 检查 Apollo 服务器地址
   - 确认应用 ID 和集群配置

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

### Loguru 日志系统

项目已集成 Loguru 现代化日志框架，提供以下特性：

- 🎨 **彩色控制台输出**：自动彩色化不同级别的日志
- 📁 **智能文件轮转**：支持按大小和时间轮转
- 🔧 **结构化日志**：支持 JSON 格式和自定义字段
- ⚡ **异步写入**：高性能异步日志写入
- 🐛 **增强异常追踪**：更详细的异常堆栈信息

#### 基本使用

```python
from kefu_ai_agents.utils.logger import get_logger, get_native_logger

# 兼容模式（适用于现有代码）
logger = get_logger(__name__)
logger.info("这是一条信息")

# 原生模式（推荐新代码使用）
logger = get_native_logger()
logger.info("用户 {user} 执行了 {action}", user="张三", action="登录")
logger.success("操作成功！")  # loguru 特有的 success 级别
```

#### 性能监控

```python
from kefu_ai_agents.utils.logger import log_execution_time

@log_execution_time("用户登录")
def login_user(username, password):
    # 函数执行时间会自动记录
    pass
```

详细使用指南请参考：[Loguru 使用指南](docs/loguru_guide.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature-AmazingFeature`)
3. 提交修改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature-AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用内部许可证，仅供内部使用。

## 📞 联系方式

如有问题或建议，请联系开发团队。
