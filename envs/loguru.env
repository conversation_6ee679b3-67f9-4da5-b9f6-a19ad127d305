# Loguru日志配置示例
# 复制到你的环境配置文件中使用

# 基础日志配置
LOG_LEVEL=INFO                              # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE=logs/app.log                       # 日志文件路径
LOG_TO_CONSOLE=True                         # 是否输出到控制台

# 文件轮转配置
LOG_MAX_SIZE=10 MB                          # 单个日志文件最大大小
LOG_BACKUP_COUNT=5                          # 保留的备份文件数量
LOG_ROTATE_WHEN=midnight                    # 轮转时间: midnight, hourly, daily, weekly

# 高级配置
LOG_COMPRESSION=zip                         # 压缩格式: zip, gz, bz2
LOG_ASYNC=True                              # 是否异步写入
LOG_BACKTRACE=True                          # 是否包含异常回溯
LOG_DIAGNOSE=True                           # 是否包含诊断信息

# 开发环境配置示例
# LOG_LEVEL=DEBUG
# LOG_TO_CONSOLE=True
# LOG_FILE=logs/dev.log

# 生产环境配置示例
# LOG_LEVEL=INFO
# LOG_TO_CONSOLE=False
# LOG_FILE=/var/log/app/production.log
# LOG_MAX_SIZE=50 MB
# LOG_BACKUP_COUNT=30
# LOG_ROTATE_WHEN=midnight
