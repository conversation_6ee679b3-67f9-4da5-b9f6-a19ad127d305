"""
Loguru使用示例

展示如何在项目中使用新的loguru日志系统
"""

import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from kefu_ai_agents.utils.logger import (
    get_logger, 
    get_native_logger,
    log_execution_time,
    log_info,
    log_error,
    log_debug,
    log_warning
)
from kefu_ai_agents.utils.loguru_config import (
    log_api_request,
    log_api_response,
    log_database_query,
    log_error_with_context
)


def example_basic_logging():
    """基础日志记录示例"""
    print("\n=== 基础日志记录示例 ===")
    
    # 使用兼容的logger（与原有代码兼容）
    logger = get_logger(__name__)
    
    logger.info("这是一条信息日志")
    logger.debug("这是一条调试日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    
    # 使用原生loguru logger（推荐新代码使用）
    native_logger = get_native_logger()
    native_logger.info("使用原生loguru记录的信息")
    native_logger.success("操作成功！")  # loguru特有的success级别


def example_structured_logging():
    """结构化日志示例"""
    print("\n=== 结构化日志示例 ===")
    
    logger = get_native_logger()
    
    # 添加结构化数据
    user_data = {"user_id": 12345, "username": "test_user", "action": "login"}
    logger.info("用户登录", extra=user_data)
    
    # 使用bind添加上下文
    request_logger = logger.bind(request_id="req_123", user_id=12345)
    request_logger.info("处理用户请求")
    request_logger.info("请求处理完成")


@log_execution_time("示例函数")
def example_function_with_timing():
    """带执行时间记录的函数示例"""
    print("\n=== 函数执行时间记录示例 ===")
    time.sleep(0.1)  # 模拟一些工作
    return "函数执行完成"


def example_api_logging():
    """API日志记录示例"""
    print("\n=== API日志记录示例 ===")
    
    # 记录API请求
    log_api_request("POST", "/api/users", {"name": "张三", "email": "<EMAIL>"})
    
    # 模拟API处理时间
    start_time = time.time()
    time.sleep(0.05)
    duration = time.time() - start_time
    
    # 记录API响应
    log_api_response("POST", "/api/users", 201, duration)


def example_database_logging():
    """数据库日志记录示例"""
    print("\n=== 数据库日志记录示例 ===")
    
    # 记录数据库查询
    query = "SELECT * FROM users WHERE status = %s AND created_at > %s"
    params = ("active", "2024-01-01")
    
    start_time = time.time()
    time.sleep(0.02)  # 模拟查询时间
    duration = time.time() - start_time
    
    log_database_query(query, params, duration)


def example_error_logging():
    """错误日志记录示例"""
    print("\n=== 错误日志记录示例 ===")
    
    try:
        # 故意制造一个错误
        result = 1 / 0
    except Exception as e:
        # 记录带上下文的错误
        context = {
            "operation": "division",
            "operands": [1, 0],
            "user_id": 12345,
            "timestamp": time.time()
        }
        log_error_with_context(e, context)
        
        # 使用原生logger记录异常（包含完整堆栈）
        logger = get_native_logger()
        logger.exception("除零错误发生")


def example_convenience_functions():
    """便捷函数示例"""
    print("\n=== 便捷函数示例 ===")
    
    # 使用便捷的日志函数
    log_info("这是使用便捷函数记录的信息")
    log_debug("这是调试信息")
    log_warning("这是警告信息")
    log_error("这是错误信息")


def example_conditional_logging():
    """条件日志记录示例"""
    print("\n=== 条件日志记录示例 ===")
    
    logger = get_native_logger()
    
    # 只在特定条件下记录
    debug_mode = os.getenv("DEBUG", "False").lower() == "true"
    
    if debug_mode:
        logger.debug("调试模式已启用，记录详细信息")
    
    # 使用loguru的条件记录
    logger.opt(lazy=True).debug("延迟计算的日志: {}", lambda: expensive_computation())


def expensive_computation():
    """模拟昂贵的计算"""
    time.sleep(0.01)
    return "计算结果"


def example_custom_formatting():
    """自定义格式化示例"""
    print("\n=== 自定义格式化示例 ===")
    
    logger = get_native_logger()
    
    # 使用自定义格式
    logger.info("用户 {user} 在 {timestamp} 执行了 {action}", 
                user="张三", 
                timestamp="2024-01-15 10:30:00", 
                action="登录")
    
    # 使用字典格式化
    data = {"user": "李四", "action": "购买", "amount": 99.99}
    logger.info("交易记录: {data}", data=data)


def main():
    """主函数：运行所有示例"""
    print("Loguru日志系统使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_logging()
    example_structured_logging()
    
    result = example_function_with_timing()
    print(f"函数返回: {result}")
    
    example_api_logging()
    example_database_logging()
    example_error_logging()
    example_convenience_functions()
    example_conditional_logging()
    example_custom_formatting()
    
    print("\n" + "=" * 50)
    print("所有示例执行完成！")
    print("请查看控制台输出和日志文件（如果配置了的话）")


if __name__ == "__main__":
    main()
