"""
日志模块 - 使用loguru实现

此模块提供了基于loguru的日志功能，同时保持与原有logging接口的兼容性。
"""

import os
import sys
from dotenv import load_dotenv

# 导入loguru兼容层
from kefu_ai_agents.utils.logger_compat import (
    setup_logging,
    get_logger,
    get_native_logger,
    log_info,
    log_error,
    log_debug,
    log_warning,
    log_execution_time,
    temporary_log_level,
)

# 导出所有接口，保持向后兼容
__all__ = [
    "setup_logging",
    "get_logger",
    "get_native_logger",
    "log_info",
    "log_error",
    "log_debug",
    "log_warning",
    "log_execution_time",
    "temporary_log_level",
]
