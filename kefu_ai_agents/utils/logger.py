import logging
import os
import sys
import threading
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from dotenv import load_dotenv

# 使用标志防止重复初始化
_logging_initialized = False
_setup_lock = threading.Lock()


def setup_logging() -> None:
    """线程安全的日志设置，确保只初始化一次"""
    global _logging_initialized

    # 如果已经初始化过，直接返回
    if _logging_initialized:
        return

    with _setup_lock:
        # 双重检查避免竞态条件
        if _logging_initialized:
            return

        try:
            # 从环境变量获取日志配置
            log_level = os.getenv("LOG_LEVEL", "INFO").upper()
            log_file = os.getenv("LOG_FILE")
            log_format = os.getenv(
                "LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            log_max_size = int(os.getenv("LOG_MAX_SIZE", 10485760))  # 默认10MB
            log_backup_count = int(os.getenv("LOG_BACKUP_COUNT", 5))
            log_to_console = os.getenv("LOG_TO_CONSOLE", "True").lower() == "true"
            log_rotate_when = os.getenv("LOG_ROTATE_WHEN")
            log_rotate_interval = int(os.getenv("LOG_ROTATE_INTERVAL", 1))

            # 获取日志级别
            level = getattr(logging, log_level, logging.INFO)

            # 配置根日志记录器
            root_logger = logging.getLogger()
            root_logger.setLevel(level)

            # 移除现有处理程序以避免重复日志
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)

            # 创建格式化器
            formatter = logging.Formatter(log_format)

            # 添加控制台处理器
            if log_to_console:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setFormatter(formatter)
                root_logger.addHandler(console_handler)

            # 添加文件处理器
            if log_file:
                try:
                    log_dir = os.path.dirname(log_file)
                    if log_dir:
                        # 确保日志目录存在
                        os.makedirs(log_dir, exist_ok=True)

                    if log_rotate_when:
                        file_handler = TimedRotatingFileHandler(
                            log_file,
                            when=log_rotate_when,
                            interval=log_rotate_interval,
                            backupCount=log_backup_count,
                            utc=False,
                        )
                    else:
                        file_handler = RotatingFileHandler(
                            log_file,
                            maxBytes=log_max_size,
                            backupCount=log_backup_count,
                        )

                    file_handler.setFormatter(formatter)
                    root_logger.addHandler(file_handler)

                    # 打印确认信息，帮助调试
                    print(f"文件日志处理器已配置，路径: {log_file}, 级别: {log_level}")

                except Exception as e:
                    # 文件处理器初始化失败时回退到控制台输出错误
                    error_msg = f"日志文件初始化失败: {str(e)}"
                    print(error_msg, file=sys.stderr)

                    # 确保至少有错误输出
                    fallback_handler = logging.StreamHandler(sys.stderr)
                    fallback_handler.setFormatter(formatter)
                    root_logger.addHandler(fallback_handler)

            _logging_initialized = True

            # 创建一个测试日志
            root_logger.info(
                f"日志系统初始化完成 - 级别: {log_level}, 文件: {log_file}, 控制台: {log_to_console}"
            )

        except Exception as e:
            error_msg = f"初始化日志系统失败: {str(e)}"
            print(error_msg, file=sys.stderr)
            # 确保基本日志功能可用，即使初始化失败
            logging.basicConfig(
                level=logging.INFO,
                format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                stream=sys.stderr,
            )


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志记录器"""
    # 确保日志系统已初始化
    if not _logging_initialized:
        # 显式打印环境变量加载信息，帮助调试
        print(f"正在为 {name} 初始化日志系统...")
        load_dotenv()  # 加载环境变量
        print(f"环境变量已加载，LOG_FILE={os.getenv('LOG_FILE')}")
        setup_logging()
    return logging.getLogger(name)
