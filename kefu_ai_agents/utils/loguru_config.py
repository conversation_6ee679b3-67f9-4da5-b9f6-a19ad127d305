"""
Loguru日志配置模块

提供基于loguru的现代化日志配置，支持：
- 彩色控制台输出
- 文件轮转
- 结构化日志
- 性能监控
- 异步日志
"""

import os
import sys
import threading
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger
from dotenv import load_dotenv

# 使用标志防止重复初始化
_loguru_initialized = False
_setup_lock = threading.Lock()


def setup_loguru() -> None:
    """线程安全的loguru设置，确保只初始化一次"""
    global _loguru_initialized

    # 如果已经初始化过，直接返回
    if _loguru_initialized:
        return

    with _setup_lock:
        # 双重检查避免竞态条件
        if _loguru_initialized:
            return

        try:
            # 移除默认处理器
            logger.remove()

            # 从环境变量获取日志配置
            log_level = os.getenv("LOG_LEVEL", "INFO").upper()
            log_file = os.getenv("LOG_FILE")
            log_to_console = os.getenv("LOG_TO_CONSOLE", "True").lower() == "true"
            log_max_size = os.getenv("LOG_MAX_SIZE", "10 MB")
            log_backup_count = int(os.getenv("LOG_BACKUP_COUNT", 5))
            log_rotate_when = os.getenv("LOG_ROTATE_WHEN")
            
            # 控制台日志格式（带颜色）
            console_format = (
                "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                "<level>{message}</level>"
            )
            
            # 文件日志格式（无颜色）
            file_format = (
                "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                "{level: <8} | "
                "{name}:{function}:{line} | "
                "{message}"
            )

            # 添加控制台处理器
            if log_to_console:
                logger.add(
                    sys.stdout,
                    format=console_format,
                    level=log_level,
                    colorize=True,
                    backtrace=True,
                    diagnose=True,
                )

            # 添加文件处理器
            if log_file:
                try:
                    log_path = Path(log_file)
                    log_path.parent.mkdir(parents=True, exist_ok=True)

                    # 根据配置选择轮转方式
                    if log_rotate_when:
                        # 时间轮转
                        rotation_config = f"{log_rotate_when} at 00:00"
                    else:
                        # 大小轮转
                        rotation_config = log_max_size

                    logger.add(
                        log_file,
                        format=file_format,
                        level=log_level,
                        rotation=rotation_config,
                        retention=log_backup_count,
                        compression="zip",  # 压缩旧日志文件
                        encoding="utf-8",
                        backtrace=True,
                        diagnose=True,
                        enqueue=True,  # 异步写入
                    )

                    logger.info(f"文件日志处理器已配置，路径: {log_file}, 级别: {log_level}")

                except Exception as e:
                    logger.error(f"日志文件初始化失败: {str(e)}")
                    # 确保至少有控制台输出
                    if not log_to_console:
                        logger.add(
                            sys.stderr,
                            format=console_format,
                            level="ERROR",
                            colorize=True,
                        )

            # 添加错误日志单独文件（如果配置了主日志文件）
            if log_file:
                error_log_file = log_file.replace(".log", "_error.log")
                logger.add(
                    error_log_file,
                    format=file_format,
                    level="ERROR",
                    rotation=log_max_size,
                    retention=log_backup_count,
                    compression="zip",
                    encoding="utf-8",
                    backtrace=True,
                    diagnose=True,
                    enqueue=True,
                )

            _loguru_initialized = True

            # 创建一个测试日志
            logger.info(
                f"Loguru日志系统初始化完成 - 级别: {log_level}, 文件: {log_file}, 控制台: {log_to_console}"
            )

        except Exception as e:
            # 确保基本日志功能可用，即使初始化失败
            logger.remove()
            logger.add(
                sys.stderr,
                format="<red>{time} | {level} | {message}</red>",
                level="ERROR",
            )
            logger.error(f"初始化Loguru日志系统失败: {str(e)}")


def get_loguru_logger(name: Optional[str] = None):
    """获取loguru日志记录器
    
    Args:
        name: 日志记录器名称（可选，loguru会自动处理）
    
    Returns:
        loguru.Logger: 配置好的日志记录器
    """
    # 确保日志系统已初始化
    if not _loguru_initialized:
        load_dotenv()  # 加载环境变量
        setup_loguru()
    
    # loguru使用全局logger实例，可以通过bind添加上下文
    if name:
        return logger.bind(logger_name=name)
    return logger


def log_function_call(func_name: str, args: tuple = (), kwargs: dict = None):
    """装饰器：记录函数调用"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            logger.debug(f"调用函数 {func_name}, 参数: args={args}, kwargs={kwargs}")
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"函数 {func_name} 执行完成，耗时: {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"函数 {func_name} 执行失败，耗时: {duration:.3f}s, 错误: {e}")
                raise
        return wrapper
    return decorator


def configure_third_party_loggers():
    """配置第三方库的日志级别"""
    import logging
    
    # 设置第三方库的日志级别，避免过多噪音
    third_party_loggers = [
        "urllib3.connectionpool",
        "requests.packages.urllib3",
        "asyncio",
        "uvicorn.access",
    ]
    
    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(logging.WARNING)


# 导入时间模块用于性能监控
import time

# 创建一些便捷的日志函数
def log_api_request(method: str, path: str, params: Dict[str, Any] = None):
    """记录API请求"""
    logger.info(f"API请求: {method} {path}", extra={"params": params})


def log_api_response(method: str, path: str, status_code: int, duration: float):
    """记录API响应"""
    logger.info(
        f"API响应: {method} {path} - {status_code} ({duration:.3f}s)",
        extra={"status_code": status_code, "duration": duration}
    )


def log_database_query(query: str, params: tuple = None, duration: float = None):
    """记录数据库查询"""
    if duration is not None:
        logger.debug(f"数据库查询 ({duration:.3f}s): {query[:100]}...", extra={"params": params})
    else:
        logger.debug(f"数据库查询: {query[:100]}...", extra={"params": params})


def log_error_with_context(error: Exception, context: Dict[str, Any] = None):
    """记录带上下文的错误"""
    logger.error(f"发生错误: {str(error)}", extra={"context": context})


# 初始化时配置第三方库日志
configure_third_party_loggers()
