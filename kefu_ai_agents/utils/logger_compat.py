"""
日志兼容性模块

提供从标准logging到loguru的平滑迁移，保持API兼容性
"""

import os
import sys
from typing import Optional
from loguru import logger as loguru_logger
from kefu_ai_agents.utils.loguru_config import setup_loguru, get_loguru_logger


class LoguruCompatLogger:
    """
    兼容标准logging.Logger接口的loguru包装器
    
    这个类提供了与标准logging.Logger相同的接口，
    但底层使用loguru实现，便于现有代码迁移
    """
    
    def __init__(self, name: str):
        self.name = name
        self._logger = get_loguru_logger(name)
    
    def debug(self, msg, *args, **kwargs):
        """Debug级别日志"""
        if args:
            msg = msg % args
        self._logger.debug(msg, **kwargs)
    
    def info(self, msg, *args, **kwargs):
        """Info级别日志"""
        if args:
            msg = msg % args
        self._logger.info(msg, **kwargs)
    
    def warning(self, msg, *args, **kwargs):
        """Warning级别日志"""
        if args:
            msg = msg % args
        self._logger.warning(msg, **kwargs)
    
    def warn(self, msg, *args, **kwargs):
        """Warning级别日志（别名）"""
        self.warning(msg, *args, **kwargs)
    
    def error(self, msg, *args, **kwargs):
        """Error级别日志"""
        if args:
            msg = msg % args
        self._logger.error(msg, **kwargs)
    
    def exception(self, msg, *args, **kwargs):
        """Exception级别日志（包含异常堆栈）"""
        if args:
            msg = msg % args
        self._logger.exception(msg, **kwargs)
    
    def critical(self, msg, *args, **kwargs):
        """Critical级别日志"""
        if args:
            msg = msg % args
        self._logger.critical(msg, **kwargs)
    
    def fatal(self, msg, *args, **kwargs):
        """Fatal级别日志（别名）"""
        self.critical(msg, *args, **kwargs)
    
    def log(self, level, msg, *args, **kwargs):
        """通用日志方法"""
        if args:
            msg = msg % args
        
        # 将数字级别转换为loguru级别名称
        level_map = {
            10: "DEBUG",
            20: "INFO", 
            30: "WARNING",
            40: "ERROR",
            50: "CRITICAL"
        }
        
        if isinstance(level, int):
            level = level_map.get(level, "INFO")
        
        self._logger.log(level, msg, **kwargs)
    
    def setLevel(self, level):
        """设置日志级别（兼容性方法，loguru全局配置）"""
        # loguru的级别是全局配置的，这里只是为了兼容性
        pass
    
    def isEnabledFor(self, level):
        """检查是否启用指定级别（简化实现）"""
        return True
    
    def getEffectiveLevel(self):
        """获取有效日志级别（简化实现）"""
        return 20  # INFO级别
    
    def addHandler(self, handler):
        """添加处理器（兼容性方法，loguru不需要）"""
        pass
    
    def removeHandler(self, handler):
        """移除处理器（兼容性方法，loguru不需要）"""
        pass


# 使用标志防止重复初始化
_compat_initialized = False


def setup_logging() -> None:
    """
    兼容性函数：设置日志系统
    
    这个函数保持与原有logger.py中setup_logging()相同的接口，
    但底层使用loguru实现
    """
    global _compat_initialized
    
    if _compat_initialized:
        return
    
    # 初始化loguru
    setup_loguru()
    _compat_initialized = True


def get_logger(name: str) -> LoguruCompatLogger:
    """
    兼容性函数：获取日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        LoguruCompatLogger: 兼容标准logging接口的loguru包装器
    """
    # 确保日志系统已初始化
    if not _compat_initialized:
        from dotenv import load_dotenv
        load_dotenv()  # 加载环境变量
        setup_logging()
    
    return LoguruCompatLogger(name)


# 提供直接的loguru访问
def get_native_logger(name: Optional[str] = None):
    """
    获取原生loguru日志记录器
    
    Args:
        name: 日志记录器名称（可选）
    
    Returns:
        loguru.Logger: 原生loguru日志记录器
    """
    return get_loguru_logger(name)


# 便捷的日志记录函数
def log_info(msg: str, **kwargs):
    """便捷的info日志函数"""
    loguru_logger.info(msg, **kwargs)


def log_error(msg: str, **kwargs):
    """便捷的error日志函数"""
    loguru_logger.error(msg, **kwargs)


def log_debug(msg: str, **kwargs):
    """便捷的debug日志函数"""
    loguru_logger.debug(msg, **kwargs)


def log_warning(msg: str, **kwargs):
    """便捷的warning日志函数"""
    loguru_logger.warning(msg, **kwargs)


# 性能监控装饰器
def log_execution_time(func_name: Optional[str] = None):
    """
    装饰器：记录函数执行时间
    
    Args:
        func_name: 自定义函数名称
    """
    def decorator(func):
        import time
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            name = func_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                loguru_logger.info(f"函数 {name} 执行完成，耗时: {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                loguru_logger.error(f"函数 {name} 执行失败，耗时: {duration:.3f}s, 错误: {e}")
                raise
        
        return wrapper
    return decorator


# 上下文管理器：临时改变日志级别
class temporary_log_level:
    """临时改变日志级别的上下文管理器"""
    
    def __init__(self, level: str):
        self.level = level
        self.original_handlers = []
    
    def __enter__(self):
        # 保存当前处理器配置
        # 注意：这是简化实现，实际使用中可能需要更复杂的逻辑
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始配置
        pass


# 导出主要接口，保持向后兼容
__all__ = [
    'setup_logging',
    'get_logger', 
    'get_native_logger',
    'log_info',
    'log_error', 
    'log_debug',
    'log_warning',
    'log_execution_time',
    'LoguruCompatLogger',
    'temporary_log_level'
]
