import time
from fastapi import APIRouter
from kefu_ai_agents.api.schemas.common_schemas import ApiResponse
from kefu_ai_agents.api.schemas.session_schemas import QaMessageIntentRequest
from kefu_ai_agents.service.qa_service import get_message_intent
from kefu_ai_agents.utils.logger import get_logger

logger = get_logger(__name__)

# 创建路由器实例
router = APIRouter(prefix="/qa", tags=["qa"])


@router.post("/message/intent", response_model=ApiResponse)
async def message_intent(request: QaMessageIntentRequest):
    """获取消息意图
    Args:
        request: QaMessageIntentRequest 请求体
    Returns:
        ApiResponse: 包含消息意图的响应
    """
    try:
        logger.info("message_intent请求参数: %s", request.model_dump_json())
        start_time = time.time()
        # 调用服务获取意图
        intent = get_message_intent(request)

        duration = time.time() - start_time
        return ApiResponse(
            code=200,
            message=f"success. elapsed: {duration:.2f}s",
            data=intent,
        )

    except ValueError as ve:
        return ApiResponse(
            code=400,
            message=f"参数错误: {str(ve)}",
            data=None,
        )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"获取消息意图失败: {str(e)}",
            data=None,
        )
