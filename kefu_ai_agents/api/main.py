# -- coding: utf-8 --
from pathlib import Path
import sys
import os
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv

from kefu_ai_agents.api.schemas.common_schemas import ApiResponse
from kefu_ai_agents.api.routers import test_api, qa_api
from kefu_ai_agents.utils.logger import setup_logging, get_logger

# 加载环境变量
load_dotenv()
# 初始化日志系统
setup_logging()
# 获取应用日志记录器
logger = get_logger(__name__)

# 创建 FastAPI 应用实例
app = FastAPI(
    title="客服 AI Agents 服务 API",
    description="客服 AI Agents 服务 API",
    version="1.0.0",
)

# 应用启动时记录日志
logger.info("FastAPI 应用已创建，日志系统正常工作")

# 注册路由
app.include_router(test_api.router)
app.include_router(qa_api.router)


# 健康检查接口
@app.get("/i/health", response_model=ApiResponse)
async def health_check():
    """健康检查接口"""
    return ApiResponse(
        code=200,
        message="服务运行正常",
        data={"status": "healthy", "service": "kefu-ai-agents"},
    )


# 根路径接口
@app.get("/", response_model=ApiResponse)
async def root():
    """根路径接口"""
    return ApiResponse(
        code=200,
        message="欢迎使用客服 AI Agents 服务 API",
        data={"version": "1.0.0", "documentation": "请访问 /docs 查看 API 文档"},
    )


# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    return JSONResponse(
        status_code=500,
        content={"code": 500, "message": f"服务内部错误: {str(exc)}", "data": None},
    )


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
