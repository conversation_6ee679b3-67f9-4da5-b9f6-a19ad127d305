import requests
import json
from typing import Dict, Any, Optional, List
from kefu_ai_agents.utils.logger import get_logger

logger = get_logger(__name__)


class WaiterException(Exception):
    """Waiter服务异常"""

    pass


class WaiterResponse:
    """Waiter响应数据结构"""

    def __init__(self, response_data: Dict[str, Any]):
        self.raw_data = response_data
        self.code = response_data.get("code")
        self.message = response_data.get("message")
        self.success = response_data.get("success", False)
        self.result = response_data.get("result")
        data_field = response_data.get("data", {})
        # 解析data字段
        self.trace_id = data_field.get("traceId")
        self.query_cost = data_field.get("queryCost")
        self.total = data_field.get("total", 0)
        self.data = data_field.get("data", [])
        self.request_id = data_field.get("requestId")
        self.request_cost = data_field.get("requestCost")
        self.td_table = data_field.get("tdTable")
        self.tag = data_field.get("tag")
        self.fields = data_field.get("fields")
        self.other_carrier = data_field.get("otherCarrier")
        self.query_id = data_field.get("queryId")

    def is_success(self) -> bool:
        """检查响应是否成功"""
        return self.success and self.code == "200"

    def get_data(self) -> List[Dict[str, Any]]:
        """获取响应数据列表"""
        return self.data

    def get_total(self) -> int:
        """获取总数"""
        return self.total

    def get_request_id(self) -> Optional[str]:
        """获取请求ID"""
        return self.request_id

    def get_query_cost(self) -> Optional[int]:
        """获取查询耗时"""
        return self.query_cost

    def get_request_cost(self) -> Optional[int]:
        """获取请求耗时"""
        return self.request_cost


class WaiterClient:
    def __init__(self, base_url: str, timeout: int = 30):
        """
        初始化Waiter客户端

        Args:
            base_url: waiter服务的基础URL
            timeout: 请求超时时间（秒），默认30秒
        """
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout

    def query(
        self,
        params: Optional[Dict[str, Any]] = None,
        page: int = 1,
        size: int = 1000,
        other_carrier: Optional[Dict[str, Any]] = None,
    ) -> WaiterResponse:
        """
        请求waiter服务

        Args:
            params: 参数字典，默认为空字典
            page: 页码，默认为1
            size: 每页大小，默认为1000
            other_carrier: 其他载体参数，默认为空字典

        Returns:
            WaiterResponse: 封装的响应结果

        Raises:
            WaiterException: 当请求失败或响应格式错误时抛出
        """
        if params is None:
            params = {}
        if other_carrier is None:
            other_carrier = {}

        # 构造请求体
        payload = {
            "page": page,
            "size": size,
            "params": params,
            "otherCarrier": other_carrier,
        }

        try:
            logger.info(f"Sending request to {self.base_url} with payload: {payload}")

            # 发送POST请求
            response = requests.post(
                self.base_url,
                headers={"Content-Type": "application/json"},
                data=json.dumps(payload, ensure_ascii=False),
                timeout=self.timeout,
            )

            # 检查响应状态
            response.raise_for_status()

            # 解析响应
            response_data = response.json()
            waiter_response = WaiterResponse(response_data)

            # 检查业务层面的成功状态
            if not waiter_response.is_success():
                raise WaiterException(
                    f"Waiter service returned error: code={waiter_response.code}, "
                    f"message={waiter_response.message}"
                )

            logger.info(
                f"Request successful, got {waiter_response.get_total()} records" + ""
                if waiter_response.get_total() > 5
                else f", data: {json.dumps(waiter_response.get_data())}"
            )
            return waiter_response

        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}", exc_info=True)
            raise WaiterException(f"Request failed: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse response JSON: {e}", exc_info=True)
            raise WaiterException(f"Failed to parse response JSON: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}", exc_info=True)
            raise WaiterException(f"Unexpected error: {e}")


def query_waiter(
    url: str,
    params: Optional[Dict[str, Any]],
    page: int = 1,
    size: int = 100,
    other_carrier: Optional[Dict[str, Any]] = None,
    timeout: int = 30,
) -> WaiterResponse:
    """
    远程请求waiter服务的便捷函数

    Args:
        url: 请求的URL
        params: 参数字典，默认为空字典
        page: 页码，默认为1
        size: 每页大小，默认为1000
        other_carrier: 其他载体参数，默认为空字典
        timeout: 请求超时时间（秒），默认30秒

    Returns:
        WaiterResponse: 封装的响应结果

    Raises:
        WaiterException: 当请求失败或响应格式错误时抛出
    """
    client = WaiterClient(url, timeout)
    return client.query(params, page, size, other_carrier)
